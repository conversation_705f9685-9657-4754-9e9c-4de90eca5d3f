/**
 * Omnihome Custom Styles
 * Additional styles for glassmorphism and custom components
 */

/* CSS Custom Properties for Theme System */
:root {
    --theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --glass-transition: all 0.2s ease;
}

/* Light Theme Variables */
:root,
.light-theme {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --accent-primary: #6366f1;
    --accent-secondary: #8b5cf6;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --glass-bg: rgba(255, 255, 255, 0.7);
    --glass-border: rgba(255, 255, 255, 0.3);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, #6366f1, #8b5cf6);
    --gradient-secondary: linear-gradient(135deg, #f59e0b, #ef4444);
}

/* Dark Theme Variables */
.dark-theme {
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --text-primary: #ffffff;
    --text-secondary: #b8b8d1;
    --text-muted: #8b8ba7;
    --border-color: #2d2d44;
    --accent-primary: #6366f1;
    --accent-secondary: #8b5cf6;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
    --gradient-primary: linear-gradient(135deg, #6366f1, #8b5cf6);
    --gradient-secondary: linear-gradient(135deg, #f59e0b, #ef4444);
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    transition: var(--theme-transition);
}

/* Override Tailwind's background and text colors */
.bg-bg-primary {
    background-color: var(--bg-primary) !important;
}

.bg-bg-secondary {
    background-color: var(--bg-secondary) !important;
}

.bg-bg-tertiary {
    background-color: var(--bg-tertiary) !important;
}

.text-text-primary {
    color: var(--text-primary) !important;
}

.text-text-secondary {
    color: var(--text-secondary) !important;
}

.text-text-muted {
    color: var(--text-muted) !important;
}

.border-border-color {
    border-color: var(--border-color) !important;
}

.border-glass-border {
    border-color: var(--glass-border) !important;
}

.bg-glass-bg {
    background-color: var(--glass-bg) !important;
}

.bg-primary\/80 {
    background-color: rgba(99, 102, 241, 0.8) !important;
}

.bg-accent-primary {
    background-color: var(--accent-primary) !important;
}

.text-accent-primary {
    color: var(--accent-primary) !important;
}

.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

/* Theme Transitions */
.theme-transitioning * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* Glassmorphism Components */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    transition: var(--glass-transition);
}

.glass-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

/* Button Styles */
.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--theme-transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--theme-transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--bg-tertiary);
}

/* Form Styles */
.form-input {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: var(--text-primary);
    transition: var(--theme-transition);
    width: 100%;
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

/* Theme Toggle Button */
.theme-toggle-btn {
    background: var(--bg-secondary) !important;
    backdrop-filter: blur(10px);
    border: 2px solid var(--border-color) !important;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--theme-transition);
    color: var(--text-primary) !important;
    box-shadow: var(--shadow-md);
    position: relative;
}

.theme-toggle-btn:hover {
    background: var(--accent-primary) !important;
    color: white !important;
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-primary) !important;
}

/* Make theme toggle more visible in light mode */
.light-theme .theme-toggle-btn {
    background: rgba(99, 102, 241, 0.1) !important;
    border-color: rgba(99, 102, 241, 0.3) !important;
    color: var(--accent-primary) !important;
}

/* Make theme toggle more visible in dark mode */
.dark-theme .theme-toggle-btn {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: var(--text-primary) !important;
}

.theme-icon {
    transition: var(--theme-transition);
    width: 20px;
    height: 20px;
}

.theme-icon.hidden {
    opacity: 0;
    transform: scale(0.8);
    position: absolute;
}

.theme-icon:not(.hidden) {
    opacity: 1;
    transform: scale(1);
}

/* Gradient Text */
.gradient-text {
    background: var(--gradient-primary) !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    color: transparent !important;
    display: inline-block;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-secondary) 50%, var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out;
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-secondary {
    background: var(--gradient-secondary);
}

.backdrop-blur-glass {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Responsive Design */
@media (max-width: 640px) {
    .glass-card {
        border-radius: 12px;
        margin: 0 4px;
    }

    .btn-primary,
    .btn-secondary {
        padding: 10px 20px;
        font-size: 14px;
    }

    .form-input {
        padding: 10px 14px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Focus Styles for Accessibility */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.sr-only.focus:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* Modal System */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    pointer-events: none;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    z-index: 10000;
}

.modal.modal-open {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
}

.modal.modal-closing {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: all 0.3s ease;
}

.modal-backdrop.no-backdrop {
    background: transparent;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
}

.modal-dialog {
    position: relative;
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    display: flex;
    flex-direction: column;
    transform: scale(0.9) translateY(-20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth content transitions */
.modal-content {
    transition: all 0.2s ease-in-out;
}

.modal-content.transitioning {
    opacity: 0.5;
    transform: scale(0.98);
}

.modal-open .modal-dialog {
    transform: scale(1) translateY(0);
}

.modal-content {
    display: flex;
    flex-direction: column;
    max-height: 100%;
    background: var(--glass-bg) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--glass-border);
    background: var(--bg-secondary);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: var(--theme-transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    color: var(--text-primary);
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--glass-border);
    background: var(--bg-secondary);
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* Modal Sizes */
.modal-sm .modal-dialog {
    max-width: 400px;
    width: 100%;
}

.modal-md .modal-dialog {
    max-width: 500px;
    width: 100%;
}

.modal-lg .modal-dialog {
    max-width: 700px;
    width: 100%;
}

.modal-xl .modal-dialog {
    max-width: 900px;
    width: 100%;
}

.modal-full .modal-dialog {
    max-width: 95vw;
    max-height: 95vh;
    width: 100%;
    height: 100%;
    margin: 2.5vh 2.5vw;
}

/* Mobile Responsive */
@media (max-width: 640px) {
    .modal-dialog {
        margin: 0.5rem;
        max-height: calc(100vh - 1rem);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer button {
        width: 100%;
    }
}

/* Dashboard Styles */
.dashboard-nav-item {
    color: var(--text-secondary);
    background: transparent;
    border: none;
    cursor: pointer;
}

.dashboard-nav-item:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

.dashboard-nav-item.active {
    color: var(--accent-primary);
    background: rgba(99, 102, 241, 0.1);
    font-weight: 500;
}

.dashboard-section {
    transition: all 0.3s ease;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: var(--theme-transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

/* Print Styles */
@media print {
    .no-print,
    .modal-container,
    .modal {
        display: none !important;
    }

    .glass-card {
        background: white !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .glass-card {
        border-width: 2px;
    }

    .btn-primary,
    .btn-secondary {
        border-width: 2px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .theme-transitioning * {
        transition: none !important;
    }
}

/* Force theme styles on main containers */
.light-theme {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

.dark-theme {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

/* Main page container */
.page-container {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    min-height: 100vh;
    transition: var(--theme-transition);
}

/* Override specific Tailwind classes */
.light-theme .bg-white,
.light-theme .bg-gray-50,
.light-theme .bg-gray-100 {
    background-color: var(--bg-primary) !important;
}

.light-theme .text-gray-900,
.light-theme .text-black {
    color: var(--text-primary) !important;
}

.dark-theme .bg-white,
.dark-theme .bg-gray-50,
.dark-theme .bg-gray-100 {
    background-color: var(--bg-primary) !important;
}

.dark-theme .text-gray-900,
.dark-theme .text-black {
    color: var(--text-primary) !important;
}

/* Dark Mode Specific Adjustments */
.dark-theme .glass-card {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark-theme .btn-primary {
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
}

.dark-theme .btn-primary:hover {
    box-shadow: 0 8px 30px rgba(99, 102, 241, 0.4);
}

/* Light Mode Specific Adjustments */
.light-theme .glass-card {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.light-theme .btn-primary {
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.2);
}

.light-theme .btn-primary:hover {
    box-shadow: 0 8px 30px rgba(99, 102, 241, 0.3);
}

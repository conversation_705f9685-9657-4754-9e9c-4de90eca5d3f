<?php
/**
 * Omnihome Authentication API
 * Handles user registration, login, logout, and session management
 *
 * Note: This file is kept for backward compatibility.
 * New API endpoints should use the router system.
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once 'controllers/BaseController.php';

// Set JSON response headers
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');

// CORS headers for development
if (ENVIRONMENT === 'development') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, X-CSRF-Token');
}

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Get request method and action
    $method = $_SERVER['REQUEST_METHOD'];
    $action = '';

    if ($method === 'GET') {
        $action = $_GET['action'] ?? '';
    } elseif ($method === 'POST') {
        $rawInput = file_get_contents('php://input');
        $input = json_decode($rawInput, true);

        // Check for JSON decode errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON input: ' . json_last_error_msg(), 400);
        }

        $action = $input['action'] ?? '';
    }

    // Validate CSRF token for state-changing operations
    if (in_array($action, ['login', 'register']) && $method === 'POST') {
        $csrfToken = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        if (!validateCSRFToken($csrfToken)) {
            throw new Exception('Invalid CSRF token', 403);
        }
    }

    // Route to appropriate handler
    switch ($action) {
        case 'register':
            handleRegister($input);
            break;

        case 'login':
            handleLogin($input);
            break;

        case 'logout':
            handleLogout();
            break;

        case 'check':
            handleAuthCheck();
            break;

        default:
            throw new Exception('Invalid action', 400);
    }

} catch (Exception $e) {
    http_response_code($e->getCode() ?: 500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Handle user registration
 */
function handleRegister($data) {
    global $pdo;

    // Validate required fields
    $required = ['first_name', 'last_name', 'email', 'password'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            throw new Exception("$field is required", 400);
        }
    }

    // Validate email format
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format', 400);
    }

    // Validate password strength
    if (strlen($data['password']) < 8) {
        throw new Exception('Password must be at least 8 characters long', 400);
    }

    // Sanitize input
    $firstName = trim($data['first_name']);
    $lastName = trim($data['last_name']);
    $email = strtolower(trim($data['email']));
    $password = $data['password'];

    // Check if email already exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        throw new Exception('Email address is already registered', 409);
    }

    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    // Insert user
    $stmt = $pdo->prepare("
        INSERT INTO users (first_name, last_name, email, password, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
    ");

    if (!$stmt->execute([$firstName, $lastName, $email, $hashedPassword])) {
        throw new Exception('Failed to create user account', 500);
    }

    $userId = $pdo->lastInsertId();

    // Get user data
    $user = getUserById($userId);

    // Create session
    createUserSession($user);

    // Log registration
    error_log("User registered: {$email} (ID: {$userId})");

    echo json_encode([
        'success' => true,
        'message' => 'Account created successfully',
        'user' => [
            'id' => $user['id'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'email' => $user['email'],
            'role' => $user['role']
        ]
    ]);
}

/**
 * Handle user login
 */
function handleLogin($data) {
    global $pdo;

    // Validate required fields
    if (empty($data['email']) || empty($data['password'])) {
        throw new Exception('Email and password are required', 400);
    }

    $email = strtolower(trim($data['email']));
    $password = $data['password'];
    $remember = $data['remember'] ?? false;

    // Get user by email
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user || !password_verify($password, $user['password'])) {
        // Log failed login attempt
        error_log("Failed login attempt for email: {$email}");
        throw new Exception('Invalid email or password', 401);
    }

    // Update last login
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->execute([$user['id']]);

    // Create session
    createUserSession($user, $remember);

    // Log successful login
    error_log("User logged in: {$email} (ID: {$user['id']})");

    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'user' => [
            'id' => $user['id'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'email' => $user['email'],
            'role' => $user['role']
        ]
    ]);
}

/**
 * Handle user logout
 */
function handleLogout() {
    if (isLoggedIn()) {
        $userId = $_SESSION['user_id'];
        $email = $_SESSION['user_email'] ?? 'unknown';

        // Destroy session
        destroyUserSession();

        // Log logout
        error_log("User logged out: {$email} (ID: {$userId})");

        echo json_encode([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'Already logged out'
        ]);
    }
}

/**
 * Handle authentication check
 */
function handleAuthCheck() {
    if (isLoggedIn()) {
        $user = getUserById($_SESSION['user_id']);
        echo json_encode([
            'success' => true,
            'authenticated' => true,
            'user' => [
                'id' => $user['id'],
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name'],
                'email' => $user['email'],
                'role' => $user['role']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'authenticated' => false
        ]);
    }
}

/**
 * Get user by ID
 */
function getUserById($id) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND status = 'active'");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Create user session
 */
function createUserSession($user, $remember = false) {
    // Regenerate session ID for security
    session_regenerate_id(true);

    // Set session variables
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_first_name'] = $user['first_name'];
    $_SESSION['user_last_name'] = $user['last_name'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['login_time'] = time();

    // Set remember me cookie if requested
    if ($remember) {
        $cookieValue = base64_encode($user['id'] . ':' . hash('sha256', $user['email'] . $user['password']));
        setcookie('remember_user', $cookieValue, time() + (30 * 24 * 60 * 60), '/', '', true, true);
    }
}

/**
 * Destroy user session
 */
function destroyUserSession() {
    // Clear session variables
    $_SESSION = [];

    // Destroy session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Clear remember me cookie
    setcookie('remember_user', '', time() - 3600, '/', '', true, true);

    // Destroy session
    session_destroy();

    // Start new session
    session_start();
}
?>

<?php
/**
 * Omnihome Global Header
 * Includes theme system and navigation
 */

// Include configuration and theme handler
require_once 'config/config.php';
require_once 'config/paystack.php';
require_once 'includes/theme-handler.php';

// Get current theme
$currentTheme = $themeHandler->getCurrentTheme();
?>
<!DOCTYPE html>
<html lang="en" class="theme-<?php echo $currentTheme; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Omnihome - Your one-stop shop for everything home. Electronics, furniture, garden supplies and more.">
    <meta name="keywords" content="ecommerce, home, electronics, furniture, garden, shopping, Ghana">
    <meta name="author" content="Omnihome">
    <meta name="theme-color" content="<?php echo $currentTheme === 'dark' ? '#0f0f23' : '#ffffff'; ?>">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo APP_URL; ?>">
    <meta property="og:title" content="Omnihome - Everything for Your Home">
    <meta property="og:description" content="Your one-stop shop for everything home. Electronics, furniture, garden supplies and more.">
    <meta property="og:image" content="<?php echo APP_URL; ?>/assets/images/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo APP_URL; ?>">
    <meta property="twitter:title" content="Omnihome - Everything for Your Home">
    <meta property="twitter:description" content="Your one-stop shop for everything home. Electronics, furniture, garden supplies and more.">
    <meta property="twitter:image" content="<?php echo APP_URL; ?>/assets/images/og-image.jpg">

    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - Everything for Your Home'; ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">

    <!-- Critical CSS for no-flash theme switching -->
    <?php echo $themeHandler->getCriticalCSS(); ?>

    <!-- Preload critical resources -->
    <link rel="preload" href="assets/js/theme.js" as="script">
    <link rel="preload" href="assets/js/modal.js" as="script">
    <link rel="preload" href="assets/js/notifications.js" as="script">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            900: '#312e81'
                        }
                    },
                    fontFamily: {
                        sans: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif']
                    },
                    backdropBlur: {
                        xs: '2px'
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/custom.css">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">

    <!-- Theme data for JavaScript -->
    <script>
        window.omnihome = {
            theme: <?php echo json_encode($themeHandler->getThemeData()); ?>,
            user: <?php echo json_encode(isLoggedIn() ? [
                'id' => $_SESSION['user_id'],
                'role' => $_SESSION['user_role'] ?? 'customer'
            ] : null); ?>,
            csrf: '<?php echo generateCSRFToken(); ?>',
            currency: {
                code: '<?php echo CURRENCY_CODE; ?>',
                symbol: '<?php echo CURRENCY_SYMBOL; ?>'
            },
            paystack: {
                publicKey: '<?php echo PaystackConfig::getPublicKey(); ?>'
            }
        };
    </script>
</head>
<body class="<?php echo $currentTheme; ?>-theme theme-loading">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <!-- Header -->
    <header class="sticky top-0 z-40 bg-glass-bg backdrop-blur-md border-b border-glass-border">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-2 text-text-primary hover:text-accent-primary transition-colors">
                        <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-white">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                <polyline points="9,22 9,12 15,12 15,22"></polyline>
                            </svg>
                        </div>
                        <span class="text-xl font-bold">Omnihome</span>
                    </a>
                </div>

                <!-- Search Bar (Desktop) -->
                <div class="hidden md:flex flex-1 max-w-lg mx-8">
                    <div class="relative w-full">
                        <input
                            type="text"
                            id="search-input"
                            placeholder="Search products..."
                            class="w-full px-4 py-2 pl-10 pr-4 bg-glass-bg border border-glass-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-transparent text-text-primary placeholder-text-muted"
                        >
                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Navigation Icons -->
                <div class="flex items-center space-x-4">
                    <!-- Theme Toggle -->
                    <?php echo $themeHandler->getThemeToggleButton(); ?>

                    <!-- Cart -->
                    <button id="cart-toggle" class="relative p-2 text-text-primary hover:text-accent-primary transition-colors">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                            <line x1="3" y1="6" x2="21" y2="6"></line>
                            <path d="M16 10a4 4 0 0 1-8 0"></path>
                        </svg>
                        <span id="cart-count" class="absolute -top-1 -right-1 bg-accent-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                    </button>

                    <!-- User Menu -->
                    <?php if (isLoggedIn()): ?>
                        <div class="relative">
                            <button id="user-menu-toggle" class="flex items-center space-x-2 p-2 text-text-primary hover:text-accent-primary transition-colors">
                                <div class="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">
                                        <?php echo strtoupper(substr($_SESSION['user_first_name'] ?? 'U', 0, 1)); ?>
                                    </span>
                                </div>
                            </button>

                            <!-- User Dropdown -->
                            <div id="user-dropdown" class="absolute right-0 mt-2 w-48 bg-glass-bg backdrop-blur-md border border-glass-border rounded-lg shadow-xl hidden">
                                <div class="py-2">
                                    <a href="dashboard.php" class="block px-4 py-2 text-text-primary hover:bg-glass-bg transition-colors">Dashboard</a>
                                    <a href="dashboard.php" class="block px-4 py-2 text-text-primary hover:bg-glass-bg transition-colors">Profile</a>
                                    <a href="dashboard.php" class="block px-4 py-2 text-text-primary hover:bg-glass-bg transition-colors">Orders</a>
                                    <a href="#" class="block px-4 py-2 text-text-primary hover:bg-glass-bg transition-colors">Wishlist</a>
                                    <?php if (isAdmin()): ?>
                                        <hr class="my-2 border-border-color">
                                        <a href="/admin" class="block px-4 py-2 text-text-primary hover:bg-glass-bg transition-colors">Admin Panel</a>
                                    <?php endif; ?>
                                    <hr class="my-2 border-border-color">
                                    <a href="/api/auth.php?action=logout" class="block px-4 py-2 text-error hover:bg-glass-bg transition-colors">Logout</a>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <button id="auth-toggle" class="px-4 py-2 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5">
                            Sign In
                        </button>
                    <?php endif; ?>

                    <!-- Mobile Menu Toggle -->
                    <button id="mobile-menu-toggle" class="md:hidden p-2 text-text-primary hover:text-accent-primary transition-colors">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="3" y1="6" x2="21" y2="6"></line>
                            <line x1="3" y1="12" x2="21" y2="12"></line>
                            <line x1="3" y1="18" x2="21" y2="18"></line>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Search -->
            <div class="md:hidden pb-4">
                <div class="relative">
                    <input
                        type="text"
                        id="mobile-search-input"
                        placeholder="Search products..."
                        class="w-full px-4 py-2 pl-10 pr-4 bg-glass-bg border border-glass-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-transparent text-text-primary placeholder-text-muted"
                    >
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation Menu -->
    <div id="mobile-menu" class="fixed inset-0 z-50 bg-bg-primary/95 backdrop-blur-md hidden">
        <div class="flex flex-col h-full">
            <div class="flex items-center justify-between p-4 border-b border-border-color">
                <span class="text-lg font-semibold text-text-primary">Menu</span>
                <button id="mobile-menu-close" class="p-2 text-text-primary hover:text-accent-primary transition-colors">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>

            <nav class="flex-1 p-4">
                <div class="space-y-4">
                    <a href="/" class="block text-text-primary hover:text-accent-primary transition-colors">Home</a>
                    <a href="#" class="block text-text-primary hover:text-accent-primary transition-colors">Categories</a>
                    <a href="#" class="block text-text-primary hover:text-accent-primary transition-colors">Deals</a>
                    <a href="#" class="block text-text-primary hover:text-accent-primary transition-colors">About</a>
                    <a href="#" class="block text-text-primary hover:text-accent-primary transition-colors">Contact</a>
                </div>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <main id="main-content" class="page-container">
        <!-- Display notifications from PHP session -->
        <?php
        $notifications = getNotifications();
        if (!empty($notifications)):
        ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                <?php foreach ($notifications as $notification): ?>
                window.notifications.show(
                    '<?php echo $notification['type']; ?>',
                    '<?php echo addslashes($notification['message']); ?>',
                    '<?php echo addslashes($notification['title']); ?>'
                );
                <?php endforeach; ?>
            });
        </script>
        <?php endif; ?>

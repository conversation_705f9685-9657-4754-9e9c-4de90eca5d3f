<?php
/**
 * User Dashboard
 * User profile management and account overview
 */

// Redirect if not logged in (before any output)
require_once 'config/config.php';
require_once 'includes/functions.php';

if (!isLoggedIn()) {
    header('Location: /');
    exit;
}

$pageTitle = "Dashboard";
require_once 'includes/header.php';

$user = [
    'id' => $_SESSION['user_id'],
    'first_name' => $_SESSION['user_first_name'],
    'last_name' => $_SESSION['user_last_name'],
    'email' => $_SESSION['user_email'],
    'role' => $_SESSION['user_role']
];
?>

<div class="min-h-screen bg-bg-primary py-8">
    <div class="container mx-auto px-4">
        <!-- Dashboard Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-text-primary mb-2">
                Welcome back, <?php echo htmlspecialchars($user['first_name']); ?>!
            </h1>
            <p class="text-text-secondary">
                Manage your account, orders, and preferences
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <nav class="glass-card p-6">
                    <h3 class="text-lg font-semibold text-text-primary mb-4">Account</h3>
                    <ul class="space-y-2">
                        <li>
                            <button
                                onclick="showDashboardSection('profile')"
                                class="dashboard-nav-item active w-full text-left px-3 py-2 rounded-lg transition-colors"
                                data-section="profile"
                            >
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Profile
                            </button>
                        </li>
                        <li>
                            <button
                                onclick="showDashboardSection('orders')"
                                class="dashboard-nav-item w-full text-left px-3 py-2 rounded-lg transition-colors"
                                data-section="orders"
                            >
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                                Orders
                            </button>
                        </li>
                        <li>
                            <button
                                onclick="showDashboardSection('addresses')"
                                class="dashboard-nav-item w-full text-left px-3 py-2 rounded-lg transition-colors"
                                data-section="addresses"
                            >
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Addresses
                            </button>
                        </li>
                        <li>
                            <button
                                onclick="showDashboardSection('security')"
                                class="dashboard-nav-item w-full text-left px-3 py-2 rounded-lg transition-colors"
                                data-section="security"
                            >
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                Security
                            </button>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Profile Section -->
                <div id="profile-section" class="dashboard-section">
                    <div class="glass-card p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-semibold text-text-primary">Profile Information</h2>
                            <button onclick="editProfile()" class="btn-secondary text-sm">
                                Edit Profile
                            </button>
                        </div>

                        <div id="profile-view" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-text-secondary mb-1">First Name</label>
                                    <p class="text-text-primary" id="display-first-name"><?php echo htmlspecialchars($user['first_name']); ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-secondary mb-1">Last Name</label>
                                    <p class="text-text-primary" id="display-last-name"><?php echo htmlspecialchars($user['last_name']); ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-secondary mb-1">Email</label>
                                    <p class="text-text-primary"><?php echo htmlspecialchars($user['email']); ?></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-secondary mb-1">Phone</label>
                                    <p class="text-text-primary" id="display-phone">Not provided</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-secondary mb-1">Date of Birth</label>
                                    <p class="text-text-primary" id="display-dob">Not provided</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-text-secondary mb-1">Gender</label>
                                    <p class="text-text-primary" id="display-gender">Not provided</p>
                                </div>
                            </div>
                        </div>

                        <div id="profile-edit" class="hidden">
                            <form id="profile-form" class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="edit-first-name" class="block text-sm font-medium text-text-primary mb-2">First Name</label>
                                        <input type="text" id="edit-first-name" name="first_name" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="edit-last-name" class="block text-sm font-medium text-text-primary mb-2">Last Name</label>
                                        <input type="text" id="edit-last-name" name="last_name" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="edit-phone" class="block text-sm font-medium text-text-primary mb-2">Phone</label>
                                        <input type="tel" id="edit-phone" name="phone" class="form-input">
                                    </div>
                                    <div>
                                        <label for="edit-dob" class="block text-sm font-medium text-text-primary mb-2">Date of Birth</label>
                                        <input type="date" id="edit-dob" name="date_of_birth" class="form-input">
                                    </div>
                                    <div>
                                        <label for="edit-gender" class="block text-sm font-medium text-text-primary mb-2">Gender</label>
                                        <select id="edit-gender" name="gender" class="form-input">
                                            <option value="">Select gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                            <option value="prefer_not_to_say">Prefer not to say</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="flex gap-3 pt-4">
                                    <button type="submit" class="btn-primary">Save Changes</button>
                                    <button type="button" onclick="cancelEdit()" class="btn-secondary">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Orders Section -->
                <div id="orders-section" class="dashboard-section hidden">
                    <div class="glass-card p-6">
                        <h2 class="text-xl font-semibold text-text-primary mb-6">Order History</h2>
                        <div id="orders-content">
                            <div class="text-center py-8">
                                <svg class="w-16 h-16 mx-auto text-text-muted mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                                <p class="text-text-muted">No orders yet</p>
                                <p class="text-text-secondary text-sm mt-2">Start shopping to see your orders here</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Addresses Section -->
                <div id="addresses-section" class="dashboard-section hidden">
                    <div class="glass-card p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-semibold text-text-primary">Saved Addresses</h2>
                            <button onclick="addAddress()" class="btn-primary text-sm">
                                Add Address
                            </button>
                        </div>
                        <div id="addresses-content">
                            <div class="text-center py-8">
                                <svg class="w-16 h-16 mx-auto text-text-muted mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <p class="text-text-muted">No addresses saved</p>
                                <p class="text-text-secondary text-sm mt-2">Add an address for faster checkout</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Section -->
                <div id="security-section" class="dashboard-section hidden">
                    <div class="glass-card p-6">
                        <h2 class="text-xl font-semibold text-text-primary mb-6">Security Settings</h2>

                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium text-text-primary mb-4">Change Password</h3>
                                <form id="password-form" class="space-y-4 max-w-md">
                                    <div>
                                        <label for="current-password" class="block text-sm font-medium text-text-primary mb-2">Current Password</label>
                                        <input type="password" id="current-password" name="current_password" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="new-password" class="block text-sm font-medium text-text-primary mb-2">New Password</label>
                                        <input type="password" id="new-password" name="new_password" class="form-input" required>
                                        <p class="text-xs text-text-muted mt-1">Must be at least 8 characters long</p>
                                    </div>
                                    <div>
                                        <label for="confirm-password" class="block text-sm font-medium text-text-primary mb-2">Confirm New Password</label>
                                        <input type="password" id="confirm-password" name="confirm_password" class="form-input" required>
                                    </div>
                                    <button type="submit" class="btn-primary">Update Password</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/js/dashboard.js"></script>

<?php include 'includes/footer.php'; ?>

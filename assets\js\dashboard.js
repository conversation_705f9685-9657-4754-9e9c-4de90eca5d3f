/**
 * Dashboard JavaScript
 * Handles user dashboard functionality
 */

class Dashboard {
    constructor() {
        this.currentSection = 'profile';
        this.profileData = null;
        this.init();
    }

    /**
     * Initialize dashboard
     */
    init() {
        this.setupEventListeners();
        this.loadProfileData();
        this.showSection('profile');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Profile form
        const profileForm = document.getElementById('profile-form');
        if (profileForm) {
            profileForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateProfile();
            });
        }

        // Password form
        const passwordForm = document.getElementById('password-form');
        if (passwordForm) {
            passwordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.changePassword();
            });
        }

        // Password confirmation validation
        const newPassword = document.getElementById('new-password');
        const confirmPassword = document.getElementById('confirm-password');

        if (newPassword && confirmPassword) {
            confirmPassword.addEventListener('input', () => {
                if (confirmPassword.value && newPassword.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('Passwords do not match');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            });
        }
    }

    /**
     * Show dashboard section
     */
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.dashboard-section').forEach(section => {
            section.classList.add('hidden');
        });

        // Remove active class from all nav items
        document.querySelectorAll('.dashboard-nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Show selected section
        const section = document.getElementById(`${sectionName}-section`);
        if (section) {
            section.classList.remove('hidden');
        }

        // Add active class to nav item
        const navItem = document.querySelector(`[data-section="${sectionName}"]`);
        if (navItem) {
            navItem.classList.add('active');
        }

        this.currentSection = sectionName;

        // Load section-specific data
        switch (sectionName) {
            case 'orders':
                this.loadOrders();
                break;
            case 'addresses':
                this.loadAddresses();
                break;
        }
    }

    /**
     * Load profile data
     */
    async loadProfileData() {
        try {
            const response = await fetch('api/auth.php?action=check', {
                headers: {
                    'X-CSRF-Token': window.omnihome.csrf
                }
            });

            const result = await response.json();

            if (result.success && result.authenticated) {
                this.profileData = { user: result.user, profile: {} };
                this.updateProfileDisplay();
            } else {
                notifications.error('Failed to load profile data', 'Error');
            }
        } catch (error) {
            console.error('Error loading profile:', error);
            notifications.error('Failed to load profile data', 'Error');
        }
    }

    /**
     * Update profile display
     */
    updateProfileDisplay() {
        if (!this.profileData) return;

        const { user, profile } = this.profileData;

        // Update display elements
        document.getElementById('display-first-name').textContent = user.first_name || 'Not provided';
        document.getElementById('display-last-name').textContent = user.last_name || 'Not provided';
        document.getElementById('display-phone').textContent = profile.phone || 'Not provided';
        document.getElementById('display-dob').textContent = profile.date_of_birth || 'Not provided';
        document.getElementById('display-gender').textContent = this.formatGender(profile.gender) || 'Not provided';

        // Update form fields
        document.getElementById('edit-first-name').value = user.first_name || '';
        document.getElementById('edit-last-name').value = user.last_name || '';
        document.getElementById('edit-phone').value = profile.phone || '';
        document.getElementById('edit-dob').value = profile.date_of_birth || '';
        document.getElementById('edit-gender').value = profile.gender || '';
    }

    /**
     * Format gender for display
     */
    formatGender(gender) {
        const genderMap = {
            'male': 'Male',
            'female': 'Female',
            'other': 'Other',
            'prefer_not_to_say': 'Prefer not to say'
        };
        return genderMap[gender] || gender;
    }

    /**
     * Edit profile
     */
    editProfile() {
        document.getElementById('profile-view').classList.add('hidden');
        document.getElementById('profile-edit').classList.remove('hidden');
    }

    /**
     * Cancel edit
     */
    cancelEdit() {
        document.getElementById('profile-edit').classList.add('hidden');
        document.getElementById('profile-view').classList.remove('hidden');
        this.updateProfileDisplay(); // Reset form values
    }

    /**
     * Update profile
     */
    async updateProfile() {
        try {
            const form = document.getElementById('profile-form');
            const formData = new FormData(form);

            const data = {
                first_name: formData.get('first_name'),
                last_name: formData.get('last_name'),
                phone: formData.get('phone'),
                date_of_birth: formData.get('date_of_birth'),
                gender: formData.get('gender')
            };

            // For now, just simulate success since we don't have the full API yet
            notifications.success('Profile updated successfully', 'Success');
            this.profileData.user.first_name = data.first_name;
            this.profileData.user.last_name = data.last_name;
            this.profileData.profile.phone = data.phone;
            this.profileData.profile.date_of_birth = data.date_of_birth;
            this.profileData.profile.gender = data.gender;

            this.updateProfileDisplay();
            this.cancelEdit();

        } catch (error) {
            console.error('Error updating profile:', error);
            notifications.error('Failed to update profile', 'Error');
        }
    }

    /**
     * Change password
     */
    async changePassword() {
        try {
            const form = document.getElementById('password-form');
            const formData = new FormData(form);

            const data = {
                current_password: formData.get('current_password'),
                new_password: formData.get('new_password')
            };

            // For now, just simulate success
            notifications.success('Password changed successfully', 'Success');
            form.reset();

        } catch (error) {
            console.error('Error changing password:', error);
            notifications.error('Failed to change password', 'Error');
        }
    }

    /**
     * Load orders
     */
    async loadOrders() {
        // For now, keep the default empty state
        // This will be implemented when we have the orders system
    }

    /**
     * Load addresses
     */
    async loadAddresses() {
        // For now, keep the default empty state
        // This will be implemented when we have the addresses system
    }
}

// Global functions for onclick handlers
function showDashboardSection(section) {
    window.dashboard.showSection(section);
}

function editProfile() {
    window.dashboard.editProfile();
}

function cancelEdit() {
    window.dashboard.cancelEdit();
}

function addAddress() {
    notifications.info('Address management coming soon!', 'Feature');
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
});

    </main>

    <!-- Footer -->
    <footer class="bg-bg-secondary border-t border-border-color">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-white">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                <polyline points="9,22 9,12 15,12 15,22"></polyline>
                            </svg>
                        </div>
                        <span class="text-xl font-bold text-text-primary">Omnihome</span>
                    </div>
                    <p class="text-text-secondary text-sm leading-relaxed">
                        Your one-stop shop for everything home. From electronics to furniture, garden supplies to home decor - we've got it all.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-text-muted hover:text-accent-primary transition-colors">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-text-muted hover:text-accent-primary transition-colors">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-text-muted hover:text-accent-primary transition-colors">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-text-muted hover:text-accent-primary transition-colors">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12.007 0C5.373 0 0 5.372 0 12.007s5.373 12.006 12.007 12.006 12.006-5.372 12.006-12.006S18.641.001 12.007.001zM8.84 18.32v-6.54H6.632v6.54H8.84zm-1.105-7.43c.776 0 1.259-.514 1.259-1.155-.015-.656-.483-1.155-1.244-1.155s-1.259.499-1.259 1.155c0 .641.468 1.155 1.229 1.155h.015zm9.481 7.43v-3.64c0-1.946-1.04-2.851-2.427-2.851-1.119 0-1.62.615-1.901 1.047v-.896h-2.11c.028.595 0 6.34 0 6.34h2.11v-3.54c0-.19.014-.38.07-.515.153-.38.502-.773 1.088-.773.768 0 1.076.585 1.076 1.443v3.385h2.094z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-text-primary">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="/" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Home</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">All Categories</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Featured Products</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Best Sellers</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">New Arrivals</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Sale Items</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-text-primary">Customer Service</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Contact Us</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">FAQ</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Shipping Info</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Returns & Exchanges</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Size Guide</a></li>
                        <li><a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Track Your Order</a></li>
                    </ul>
                </div>

                <!-- Newsletter -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-text-primary">Stay Updated</h3>
                    <p class="text-text-secondary text-sm">Subscribe to get special offers, free giveaways, and updates.</p>
                    <form class="space-y-2" id="newsletter-form">
                        <input
                            type="email"
                            placeholder="Enter your email"
                            class="w-full px-3 py-2 bg-glass-bg border border-glass-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-transparent text-text-primary placeholder-text-muted text-sm"
                            required
                        >
                        <button
                            type="submit"
                            class="w-full px-4 py-2 bg-gradient-primary text-white rounded-lg hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 text-sm font-medium"
                        >
                            Subscribe
                        </button>
                    </form>

                    <!-- Payment Methods -->
                    <div class="pt-4">
                        <p class="text-text-secondary text-xs mb-2">We Accept:</p>
                        <div class="flex space-x-2">
                            <div class="w-8 h-5 bg-gradient-primary rounded flex items-center justify-center">
                                <span class="text-white text-xs font-bold">PS</span>
                            </div>
                            <div class="w-8 h-5 bg-blue-600 rounded flex items-center justify-center">
                                <span class="text-white text-xs font-bold">V</span>
                            </div>
                            <div class="w-8 h-5 bg-red-600 rounded flex items-center justify-center">
                                <span class="text-white text-xs font-bold">MC</span>
                            </div>
                            <div class="w-8 h-5 bg-green-600 rounded flex items-center justify-center">
                                <span class="text-white text-xs font-bold">MM</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Footer -->
            <div class="border-t border-border-color mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-text-secondary text-sm">
                        © <?php echo date('Y'); ?> Omnihome. All rights reserved.
                    </div>
                    <div class="flex space-x-6">
                        <a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Privacy Policy</a>
                        <a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Terms of Service</a>
                        <a href="#" class="text-text-secondary hover:text-accent-primary transition-colors text-sm">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="fixed bottom-6 right-6 w-12 h-12 bg-gradient-primary text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1 hidden z-40">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mx-auto">
            <line x1="12" y1="19" x2="12" y2="5"></line>
            <polyline points="5,12 12,5 19,12"></polyline>
        </svg>
    </button>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-bg-primary/80 backdrop-blur-sm z-50 flex items-center justify-center hidden">
        <div class="glass-card p-8 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-primary mx-auto mb-4"></div>
            <p class="text-text-primary">Loading...</p>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/modal.js"></script>
    <script src="assets/js/notifications.js"></script>
    <script src="assets/js/auth.js"></script>

    <!-- Paystack JavaScript -->
    <script src="https://js.paystack.co/v1/inline.js"></script>

    <!-- Additional JavaScript for this page -->
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $jsFile): ?>
            <script src="<?php echo $jsFile; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline JavaScript for this page -->
    <?php if (isset($inlineJS)): ?>
        <script><?php echo $inlineJS; ?></script>
    <?php endif; ?>

    <script>
        // Initialize app when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Remove theme loading class
            document.body.classList.remove('theme-loading');

            // Initialize mobile menu
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuClose = document.getElementById('mobile-menu-close');

            if (mobileMenuToggle && mobileMenu) {
                mobileMenuToggle.addEventListener('click', () => {
                    mobileMenu.classList.remove('hidden');
                });
            }

            if (mobileMenuClose && mobileMenu) {
                mobileMenuClose.addEventListener('click', () => {
                    mobileMenu.classList.add('hidden');
                });
            }

            // Initialize user dropdown
            const userMenuToggle = document.getElementById('user-menu-toggle');
            const userDropdown = document.getElementById('user-dropdown');

            if (userMenuToggle && userDropdown) {
                userMenuToggle.addEventListener('click', (e) => {
                    e.stopPropagation();
                    userDropdown.classList.toggle('hidden');
                });

                document.addEventListener('click', () => {
                    userDropdown.classList.add('hidden');
                });
            }

            // Initialize back to top button
            const backToTop = document.getElementById('back-to-top');
            if (backToTop) {
                window.addEventListener('scroll', () => {
                    if (window.pageYOffset > 300) {
                        backToTop.classList.remove('hidden');
                    } else {
                        backToTop.classList.add('hidden');
                    }
                });

                backToTop.addEventListener('click', () => {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }

            // Initialize newsletter form
            const newsletterForm = document.getElementById('newsletter-form');
            if (newsletterForm) {
                newsletterForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    const email = e.target.querySelector('input[type="email"]').value;

                    // Here you would typically send the email to your backend
                    window.showSuccess('Thank you for subscribing!', 'Newsletter');
                    e.target.reset();
                });
            }
        });
    </script>
</body>
</html>

/**
 * Omnihome Authentication System
 * Handles login, registration, and user session management
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    /**
     * Initialize authentication system
     */
    init() {
        this.setupEventListeners();
        this.checkAuthStatus();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Auth toggle button
        document.addEventListener('click', (e) => {
            if (e.target.closest('#auth-toggle')) {
                e.preventDefault();
                this.showLoginModal();
            }
        });

        // User menu toggle
        document.addEventListener('click', (e) => {
            if (e.target.closest('#user-menu-toggle')) {
                e.preventDefault();
                this.toggleUserMenu();
            }
        });

        // Close user menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#user-menu-toggle') && !e.target.closest('#user-dropdown')) {
                this.closeUserMenu();
            }
        });
    }

    /**
     * Show login modal
     */
    showLoginModal() {
        // Close any existing modal first
        if (window.modalManager.isOpen()) {
            closeModal(() => {
                // Open login modal after closing
                setTimeout(() => {
                    this.openLoginModal();
                }, 100);
            });
        } else {
            this.openLoginModal();
        }
    }

    /**
     * Show registration modal
     */
    showRegisterModal() {
        // Close any existing modal first
        if (window.modalManager.isOpen()) {
            closeModal(() => {
                // Open register modal after closing
                setTimeout(() => {
                    this.openRegisterModal();
                }, 100);
            });
        } else {
            this.openRegisterModal();
        }
    }

    /**
     * Open login modal
     */
    openLoginModal() {
        const modal = openModal('auth-modal', {
            size: 'md',
            closable: true,
            onOpen: () => {
                this.renderLoginForm();
            }
        });
    }

    /**
     * Open registration modal
     */
    openRegisterModal() {
        const modal = openModal('auth-modal', {
            size: 'md',
            closable: true,
            onOpen: () => {
                this.renderRegisterForm();
            }
        });
    }

    /**
     * Render login form
     */
    renderLoginForm() {
        const title = 'Welcome Back';
        const body = `
            <form id="login-form" class="space-y-4">
                <div>
                    <label for="login-email" class="block text-sm font-medium text-text-primary mb-2">
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="login-email"
                        name="email"
                        required
                        class="form-input"
                        placeholder="Enter your email"
                        autocomplete="email"
                    >
                </div>

                <div>
                    <label for="login-password" class="block text-sm font-medium text-text-primary mb-2">
                        Password
                    </label>
                    <div class="relative">
                        <input
                            type="password"
                            id="login-password"
                            name="password"
                            required
                            class="form-input pr-10"
                            placeholder="Enter your password"
                            autocomplete="current-password"
                        >
                        <button
                            type="button"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onclick="authManager.togglePasswordVisibility('login-password')"
                        >
                            <svg class="h-5 w-5 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input
                            id="remember-me"
                            name="remember"
                            type="checkbox"
                            class="h-4 w-4 text-accent-primary focus:ring-accent-primary border-border-color rounded"
                        >
                        <label for="remember-me" class="ml-2 block text-sm text-text-secondary">
                            Remember me
                        </label>
                    </div>

                    <div class="text-sm">
                        <a href="#" class="font-medium text-accent-primary hover:text-accent-secondary">
                            Forgot password?
                        </a>
                    </div>
                </div>

                <div id="login-error" class="hidden text-error text-sm"></div>
            </form>

            <div class="mt-6 text-center">
                <p class="text-text-secondary">
                    Don't have an account?
                    <button
                        type="button"
                        class="font-medium text-accent-primary hover:text-accent-secondary"
                        onclick="authManager.switchToRegister()"
                    >
                        Sign up
                    </button>
                </p>
            </div>
        `;

        const footer = `
            <button type="button" class="btn-secondary" onclick="closeModal()">
                Cancel
            </button>
            <button type="submit" form="login-form" class="btn-primary" id="login-submit">
                Sign In
            </button>
        `;

        setModalContent(title, body, footer);
        this.setupLoginForm();
    }

    /**
     * Render registration form
     */
    renderRegisterForm() {
        const title = 'Create Account';
        const body = `
            <form id="register-form" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="register-first-name" class="block text-sm font-medium text-text-primary mb-2">
                            First Name
                        </label>
                        <input
                            type="text"
                            id="register-first-name"
                            name="first_name"
                            required
                            class="form-input"
                            placeholder="First name"
                            autocomplete="given-name"
                        >
                    </div>

                    <div>
                        <label for="register-last-name" class="block text-sm font-medium text-text-primary mb-2">
                            Last Name
                        </label>
                        <input
                            type="text"
                            id="register-last-name"
                            name="last_name"
                            required
                            class="form-input"
                            placeholder="Last name"
                            autocomplete="family-name"
                        >
                    </div>
                </div>

                <div>
                    <label for="register-email" class="block text-sm font-medium text-text-primary mb-2">
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="register-email"
                        name="email"
                        required
                        class="form-input"
                        placeholder="Enter your email"
                        autocomplete="email"
                    >
                </div>

                <div>
                    <label for="register-password" class="block text-sm font-medium text-text-primary mb-2">
                        Password
                    </label>
                    <div class="relative">
                        <input
                            type="password"
                            id="register-password"
                            name="password"
                            required
                            class="form-input pr-10"
                            placeholder="Create a password"
                            autocomplete="new-password"
                        >
                        <button
                            type="button"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onclick="authManager.togglePasswordVisibility('register-password')"
                        >
                            <svg class="h-5 w-5 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="mt-1 text-xs text-text-muted">
                        Password must be at least 8 characters long
                    </div>
                </div>

                <div>
                    <label for="register-confirm-password" class="block text-sm font-medium text-text-primary mb-2">
                        Confirm Password
                    </label>
                    <input
                        type="password"
                        id="register-confirm-password"
                        name="confirm_password"
                        required
                        class="form-input"
                        placeholder="Confirm your password"
                        autocomplete="new-password"
                    >
                </div>

                <div class="flex items-center">
                    <input
                        id="terms-agreement"
                        name="terms"
                        type="checkbox"
                        required
                        class="h-4 w-4 text-accent-primary focus:ring-accent-primary border-border-color rounded"
                    >
                    <label for="terms-agreement" class="ml-2 block text-sm text-text-secondary">
                        I agree to the
                        <a href="#" class="text-accent-primary hover:text-accent-secondary">Terms of Service</a>
                        and
                        <a href="#" class="text-accent-primary hover:text-accent-secondary">Privacy Policy</a>
                    </label>
                </div>

                <div id="register-error" class="hidden text-error text-sm"></div>
            </form>

            <div class="mt-6 text-center">
                <p class="text-text-secondary">
                    Already have an account?
                    <button
                        type="button"
                        class="font-medium text-accent-primary hover:text-accent-secondary"
                        onclick="authManager.switchToLogin()"
                    >
                        Sign in
                    </button>
                </p>
            </div>
        `;

        const footer = `
            <button type="button" class="btn-secondary" onclick="closeModal()">
                Cancel
            </button>
            <button type="submit" form="register-form" class="btn-primary" id="register-submit">
                Create Account
            </button>
        `;

        setModalContent(title, body, footer);
        this.setupRegisterForm();
    }

    /**
     * Switch to register form (smooth transition)
     */
    switchToRegister() {
        const modal = window.modalManager.getActiveModal();
        if (modal) {
            const content = modal.element.querySelector('.modal-content');

            // Add transition class
            content.classList.add('transitioning');

            setTimeout(() => {
                this.renderRegisterForm();
                // Remove transition class
                content.classList.remove('transitioning');
            }, 200);
        } else {
            this.showRegisterModal();
        }
    }

    /**
     * Switch to login form (smooth transition)
     */
    switchToLogin() {
        const modal = window.modalManager.getActiveModal();
        if (modal) {
            const content = modal.element.querySelector('.modal-content');

            // Add transition class
            content.classList.add('transitioning');

            setTimeout(() => {
                this.renderLoginForm();
                // Remove transition class
                content.classList.remove('transitioning');
            }, 200);
        } else {
            this.showLoginModal();
        }
    }

    /**
     * Setup login form
     */
    setupLoginForm() {
        const form = document.getElementById('login-form');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleLogin(form);
        });
    }

    /**
     * Setup register form
     */
    setupRegisterForm() {
        const form = document.getElementById('register-form');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleRegister(form);
        });

        // Password confirmation validation
        const password = form.querySelector('#register-password');
        const confirmPassword = form.querySelector('#register-confirm-password');

        confirmPassword.addEventListener('input', () => {
            if (confirmPassword.value && password.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        });
    }

    /**
     * Handle login
     */
    async handleLogin(form) {
        const submitBtn = document.getElementById('login-submit');
        const errorDiv = document.getElementById('login-error');

        try {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Signing in...';
            errorDiv.classList.add('hidden');

            const formData = new FormData(form);
            const response = await fetch('/api/auth.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': window.omnihome.csrf
                },
                body: JSON.stringify({
                    action: 'login',
                    email: formData.get('email'),
                    password: formData.get('password'),
                    remember: formData.get('remember') === 'on'
                })
            });

            const result = await response.json();

            if (result.success) {
                notifications.success('Welcome back!', 'Login Successful');
                closeModal();
                this.handleLoginSuccess(result.user);
            } else {
                errorDiv.textContent = result.message || 'Login failed. Please try again.';
                errorDiv.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Login error:', error);
            errorDiv.textContent = 'An error occurred. Please try again.';
            errorDiv.classList.remove('hidden');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Sign In';
        }
    }

    /**
     * Handle registration
     */
    async handleRegister(form) {
        const submitBtn = document.getElementById('register-submit');
        const errorDiv = document.getElementById('register-error');

        try {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Creating account...';
            errorDiv.classList.add('hidden');

            const formData = new FormData(form);

            // Validate passwords match
            if (formData.get('password') !== formData.get('confirm_password')) {
                throw new Error('Passwords do not match');
            }

            const response = await fetch('/api/auth.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': window.omnihome.csrf
                },
                body: JSON.stringify({
                    action: 'register',
                    first_name: formData.get('first_name'),
                    last_name: formData.get('last_name'),
                    email: formData.get('email'),
                    password: formData.get('password')
                })
            });

            const result = await response.json();

            if (result.success) {
                notifications.success('Account created successfully!', 'Welcome to Omnihome');
                closeModal();
                this.handleLoginSuccess(result.user);
            } else {
                errorDiv.textContent = result.message || 'Registration failed. Please try again.';
                errorDiv.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Registration error:', error);
            errorDiv.textContent = error.message || 'An error occurred. Please try again.';
            errorDiv.classList.remove('hidden');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Create Account';
        }
    }

    /**
     * Handle successful login
     */
    handleLoginSuccess(user) {
        this.currentUser = user;
        window.omnihome.user = user;

        // Update UI
        this.updateAuthUI();

        // Reload page to update server-side state
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }

    /**
     * Toggle password visibility
     */
    togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
    }

    /**
     * Toggle user menu
     */
    toggleUserMenu() {
        const dropdown = document.getElementById('user-dropdown');
        if (dropdown) {
            dropdown.classList.toggle('hidden');
        }
    }

    /**
     * Close user menu
     */
    closeUserMenu() {
        const dropdown = document.getElementById('user-dropdown');
        if (dropdown) {
            dropdown.classList.add('hidden');
        }
    }

    /**
     * Check authentication status
     */
    checkAuthStatus() {
        this.currentUser = window.omnihome.user;
        this.updateAuthUI();
    }

    /**
     * Update authentication UI
     */
    updateAuthUI() {
        // This will be handled by page reload for now
        // In a SPA, we would update the UI elements here
    }

    /**
     * Logout user
     */
    async logout() {
        try {
            const response = await fetch('/api/auth.php?action=logout', {
                method: 'GET',
                headers: {
                    'X-CSRF-Token': window.omnihome.csrf
                }
            });

            if (response.ok) {
                notifications.success('You have been logged out', 'Goodbye!');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        } catch (error) {
            console.error('Logout error:', error);
            notifications.error('Logout failed', 'Error');
        }
    }
}

// Initialize auth manager
window.authManager = new AuthManager();

<?php
/**
 * Omnihome Homepage
 * Main landing page with hero section, featured products, and categories
 */

$pageTitle = "Home";
include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Background Gradient -->
    <div class="absolute inset-0 bg-gradient-to-br from-accent-primary/20 via-accent-secondary/10 to-transparent"></div>

    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-accent-primary/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-secondary/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <!-- Main Heading -->
            <h1 class="text-5xl md:text-7xl font-bold text-text-primary mb-6 leading-tight">
                Everything for Your
                <span class="gradient-text">
                    Home
                </span>
            </h1>

            <!-- Subtitle -->
            <p class="text-xl md:text-2xl text-text-secondary mb-8 leading-relaxed">
                Discover amazing products for every corner of your home. From electronics to furniture, garden supplies to home decor.
            </p>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                <button class="btn-primary text-lg">
                    Shop Now
                </button>
                <button class="btn-secondary text-lg">
                    Browse Categories
                </button>
            </div>

            <!-- Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
                <div class="text-center">
                    <div class="text-3xl font-bold gradient-text mb-2">10K+</div>
                    <div class="text-text-secondary text-sm">Products</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold gradient-text mb-2">5K+</div>
                    <div class="text-text-secondary text-sm">Happy Customers</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold gradient-text mb-2">50+</div>
                    <div class="text-text-secondary text-sm">Categories</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold gradient-text mb-2">24/7</div>
                    <div class="text-text-secondary text-sm">Support</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-text-muted">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <polyline points="19,12 12,19 5,12"></polyline>
        </svg>
    </div>
</section>

<!-- Featured Categories -->
<section class="py-20 bg-bg-secondary">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-text-primary mb-4">Shop by Category</h2>
            <p class="text-text-secondary text-lg max-w-2xl mx-auto">
                Explore our wide range of categories to find exactly what you're looking for
            </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            <!-- Electronics -->
            <div class="group cursor-pointer">
                <div class="glass-card p-6 text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-white">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-text-primary mb-2">Electronics</h3>
                    <p class="text-text-muted text-sm">Latest gadgets & tech</p>
                </div>
            </div>

            <!-- Home & Garden -->
            <div class="group cursor-pointer">
                <div class="glass-card p-6 text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-white">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9,22 9,12 15,12 15,22"></polyline>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-text-primary mb-2">Home & Garden</h3>
                    <p class="text-text-muted text-sm">Furniture & decor</p>
                </div>
            </div>

            <!-- Fashion -->
            <div class="group cursor-pointer">
                <div class="glass-card p-6 text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-white">
                            <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                            <line x1="3" y1="6" x2="21" y2="6"></line>
                            <path d="M16 10a4 4 0 0 1-8 0"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-text-primary mb-2">Fashion</h3>
                    <p class="text-text-muted text-sm">Clothing & accessories</p>
                </div>
            </div>

            <!-- Sports -->
            <div class="group cursor-pointer">
                <div class="glass-card p-6 text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-white">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path>
                            <path d="M2 12h20"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-text-primary mb-2">Sports</h3>
                    <p class="text-text-muted text-sm">Fitness & outdoor gear</p>
                </div>
            </div>

            <!-- Health & Beauty -->
            <div class="group cursor-pointer">
                <div class="glass-card p-6 text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-white">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-text-primary mb-2">Health & Beauty</h3>
                    <p class="text-text-muted text-sm">Wellness products</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-20">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-text-primary mb-4">Featured Products</h2>
            <p class="text-text-secondary text-lg max-w-2xl mx-auto">
                Discover our handpicked selection of the best products
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" id="featured-products">
            <!-- Products will be loaded here via JavaScript -->
            <div class="glass-card p-6 animate-pulse">
                <div class="bg-bg-tertiary h-48 rounded-lg mb-4"></div>
                <div class="bg-bg-tertiary h-4 rounded mb-2"></div>
                <div class="bg-bg-tertiary h-4 rounded w-2/3 mb-4"></div>
                <div class="bg-bg-tertiary h-8 rounded"></div>
            </div>
            <div class="glass-card p-6 animate-pulse">
                <div class="bg-bg-tertiary h-48 rounded-lg mb-4"></div>
                <div class="bg-bg-tertiary h-4 rounded mb-2"></div>
                <div class="bg-bg-tertiary h-4 rounded w-2/3 mb-4"></div>
                <div class="bg-bg-tertiary h-8 rounded"></div>
            </div>
            <div class="glass-card p-6 animate-pulse">
                <div class="bg-bg-tertiary h-48 rounded-lg mb-4"></div>
                <div class="bg-bg-tertiary h-4 rounded mb-2"></div>
                <div class="bg-bg-tertiary h-4 rounded w-2/3 mb-4"></div>
                <div class="bg-bg-tertiary h-8 rounded"></div>
            </div>
            <div class="glass-card p-6 animate-pulse">
                <div class="bg-bg-tertiary h-48 rounded-lg mb-4"></div>
                <div class="bg-bg-tertiary h-4 rounded mb-2"></div>
                <div class="bg-bg-tertiary h-4 rounded w-2/3 mb-4"></div>
                <div class="bg-bg-tertiary h-8 rounded"></div>
            </div>
        </div>

        <div class="text-center mt-12">
            <button class="btn-secondary">
                View All Products
            </button>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-bg-secondary">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-success/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-success">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                        <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                        <line x1="12" y1="22.08" x2="12" y2="12"></line>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-text-primary mb-2">Free Shipping</h3>
                <p class="text-text-secondary">Free shipping on orders over ₵200</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-accent-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-accent-primary">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12,6 12,12 16,14"></polyline>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-text-primary mb-2">24/7 Support</h3>
                <p class="text-text-secondary">Round-the-clock customer service</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-warning/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-warning">
                        <path d="M9 12l2 2 4-4"></path>
                        <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                        <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                        <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"></path>
                        <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-text-primary mb-2">Secure Payment</h3>
                <p class="text-text-secondary">Safe and secure payment processing</p>
            </div>
        </div>
    </div>
</section>

<script>
// Load featured products
document.addEventListener('DOMContentLoaded', function() {
    // Simulate loading featured products
    setTimeout(() => {
        const container = document.getElementById('featured-products');
        container.innerHTML = `
            <div class="glass-card group cursor-pointer overflow-hidden">
                <div class="relative overflow-hidden">
                    <img src="https://via.placeholder.com/300x200/6366f1/ffffff?text=Product+1" alt="Product 1" class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300">
                    <div class="absolute top-2 right-2 bg-error text-white px-2 py-1 rounded-full text-xs font-semibold">-20%</div>
                </div>
                <div class="p-6">
                    <h3 class="font-semibold text-text-primary mb-2">Smart Home Speaker</h3>
                    <p class="text-text-muted text-sm mb-4">Voice-controlled smart speaker with premium sound</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-text-primary">₵299</span>
                            <span class="text-sm text-text-muted line-through">₵399</span>
                        </div>
                        <button class="btn-primary text-sm">
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>

            <div class="glass-card group cursor-pointer overflow-hidden">
                <div class="relative overflow-hidden">
                    <img src="https://via.placeholder.com/300x200/8b5cf6/ffffff?text=Product+2" alt="Product 2" class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300">
                    <div class="absolute top-2 right-2 bg-success text-white px-2 py-1 rounded-full text-xs font-semibold">New</div>
                </div>
                <div class="p-6">
                    <h3 class="font-semibold text-text-primary mb-2">Wireless Headphones</h3>
                    <p class="text-text-muted text-sm mb-4">Premium noise-cancelling wireless headphones</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-text-primary">₵199</span>
                        </div>
                        <button class="btn-primary text-sm">
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>

            <div class="glass-card group cursor-pointer overflow-hidden">
                <div class="relative overflow-hidden">
                    <img src="https://via.placeholder.com/300x200/f59e0b/ffffff?text=Product+3" alt="Product 3" class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300">
                </div>
                <div class="p-6">
                    <h3 class="font-semibold text-text-primary mb-2">Smart Watch</h3>
                    <p class="text-text-muted text-sm mb-4">Fitness tracking smartwatch with heart rate monitor</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-text-primary">₵149</span>
                        </div>
                        <button class="btn-primary text-sm">
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>

            <div class="glass-card group cursor-pointer overflow-hidden">
                <div class="relative overflow-hidden">
                    <img src="https://via.placeholder.com/300x200/ef4444/ffffff?text=Product+4" alt="Product 4" class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300">
                    <div class="absolute top-2 right-2 bg-warning text-white px-2 py-1 rounded-full text-xs font-semibold">Hot</div>
                </div>
                <div class="p-6">
                    <h3 class="font-semibold text-text-primary mb-2">Gaming Mouse</h3>
                    <p class="text-text-muted text-sm mb-4">High-precision gaming mouse with RGB lighting</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg font-bold text-text-primary">₵89</span>
                        </div>
                        <button class="btn-primary text-sm">
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
